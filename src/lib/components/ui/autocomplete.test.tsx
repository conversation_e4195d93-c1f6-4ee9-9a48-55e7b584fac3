import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AutocompleteInput } from './autocomplete';

describe('AutocompleteInput', () => {
  const mockSuggestions = ['Apple', 'Banana', 'Cherry', 'Date', 'Elderberry'];

  it('renders input field with placeholder', () => {
    render(
      <AutocompleteInput
        suggestions={mockSuggestions}
        placeholder="Type to search..."
      />
    );

    const input = screen.getByPlaceholderText('Type to search...');
    expect(input).toBeInTheDocument();
  });

  it('shows suggestions when typing', async () => {
    const user = userEvent.setup();
    render(
      <AutocompleteInput
        suggestions={mockSuggestions}
        placeholder="Type to search..."
      />
    );

    const input = screen.getByPlaceholderText('Type to search...');
    await user.type(input, 'a');

    await waitFor(() => {
      expect(screen.getByText('Apple')).toBeInTheDocument();
      expect(screen.getByText('Banana')).toBeInTheDocument();
    });
  });

  it('filters suggestions based on input', async () => {
    const user = userEvent.setup();
    render(
      <AutocompleteInput
        suggestions={mockSuggestions}
        placeholder="Type to search..."
      />
    );

    const input = screen.getByPlaceholderText('Type to search...');
    await user.type(input, 'ap');

    await waitFor(() => {
      expect(screen.getByText('Apple')).toBeInTheDocument();
      expect(screen.queryByText('Banana')).not.toBeInTheDocument();
    });
  });

  it('calls onChange when suggestion is clicked', async () => {
    const user = userEvent.setup();
    const mockOnChange = vi.fn();
    render(
      <AutocompleteInput
        suggestions={mockSuggestions}
        placeholder="Type to search..."
        onChange={mockOnChange}
      />
    );

    const input = screen.getByPlaceholderText('Type to search...');
    await user.type(input, 'a');

    await waitFor(() => {
      expect(screen.getByText('Apple')).toBeInTheDocument();
    });

    await user.click(screen.getByText('Apple'));

    expect(mockOnChange).toHaveBeenCalledWith('Apple');
    expect(input).toHaveValue('Apple');
  });

  it('shows suggestions on focus if there are filtered suggestions', async () => {
    const user = userEvent.setup();
    render(
      <AutocompleteInput
        suggestions={mockSuggestions}
        placeholder="Type to search..."
        defaultValue="a"
      />
    );

    const input = screen.getByPlaceholderText('Type to search...');
    await user.click(input);

    await waitFor(() => {
      expect(screen.getByText('Apple')).toBeInTheDocument();
      expect(screen.getByText('Banana')).toBeInTheDocument();
    });
  });

  it('hides suggestions when clicking outside', async () => {
    const user = userEvent.setup();
    render(
      <div>
        <AutocompleteInput
          suggestions={mockSuggestions}
          placeholder="Type to search..."
        />
        <button>Outside button</button>
      </div>
    );

    const input = screen.getByPlaceholderText('Type to search...');
    await user.type(input, 'a');

    await waitFor(() => {
      expect(screen.getByText('Apple')).toBeInTheDocument();
    });

    await user.click(screen.getByText('Outside button'));

    await waitFor(() => {
      expect(screen.queryByText('Apple')).not.toBeInTheDocument();
    });
  });

  describe('keyboard navigation', () => {
    it('shows suggestions when pressing arrow down', async () => {
      render(
        <AutocompleteInput
          suggestions={mockSuggestions}
          placeholder="Type to search..."
          defaultValue="a"
        />
      );

      const input = screen.getByPlaceholderText('Type to search...');
      fireEvent.keyDown(input, { key: 'ArrowDown' });

      await waitFor(() => {
        expect(screen.getByText('Apple')).toBeInTheDocument();
        expect(screen.getByText('Banana')).toBeInTheDocument();
      });
    });

    it('shows suggestions when pressing arrow up', async () => {
      render(
        <AutocompleteInput
          suggestions={mockSuggestions}
          placeholder="Type to search..."
          defaultValue="a"
        />
      );

      const input = screen.getByPlaceholderText('Type to search...');
      fireEvent.keyDown(input, { key: 'ArrowUp' });

      await waitFor(() => {
        expect(screen.getByText('Apple')).toBeInTheDocument();
        expect(screen.getByText('Banana')).toBeInTheDocument();
      });
    });

    it('hides suggestions when pressing escape', async () => {
      const user = userEvent.setup();
      render(
        <AutocompleteInput
          suggestions={mockSuggestions}
          placeholder="Type to search..."
        />
      );

      const input = screen.getByPlaceholderText('Type to search...');
      await user.type(input, 'a');

      await waitFor(() => {
        expect(screen.getByText('Apple')).toBeInTheDocument();
      });

      fireEvent.keyDown(input, { key: 'Escape' });

      await waitFor(() => {
        expect(screen.queryByText('Apple')).not.toBeInTheDocument();
      });
    });

    it('selects highlighted suggestion when pressing enter', async () => {
      const user = userEvent.setup();
      const mockOnChange = vi.fn();
      render(
        <AutocompleteInput
          suggestions={mockSuggestions}
          placeholder="Type to search..."
          onChange={mockOnChange}
        />
      );

      const input = screen.getByPlaceholderText('Type to search...');
      await user.type(input, 'ap');

      await waitFor(() => {
        expect(screen.getByText('Apple')).toBeInTheDocument();
      });

      // Simulate arrow down to highlight first item (Apple)
      fireEvent.keyDown(input, { key: 'ArrowDown' });
      
      // Wait a bit for cmdk to process the navigation
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Press enter to select
      fireEvent.keyDown(input, { key: 'Enter' });

      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith('Apple');
        expect(input).toHaveValue('Apple');
        expect(screen.queryByText('Apple')).not.toBeInTheDocument(); // suggestions should be hidden
      });
    });
  });

  it('uses defaultValue correctly', () => {
    render(
      <AutocompleteInput
        suggestions={mockSuggestions}
        placeholder="Type to search..."
        defaultValue="Cherry"
      />
    );

    const input = screen.getByPlaceholderText('Type to search...');
    expect(input).toHaveValue('Cherry');
  });

  it('applies custom className', () => {
    render(
      <AutocompleteInput
        suggestions={mockSuggestions}
        placeholder="Type to search..."
        className="custom-class"
      />
    );

    const input = screen.getByPlaceholderText('Type to search...');
    expect(input).toHaveClass('custom-class');
  });
});
